#!/usr/bin/env python3
"""
Test script to demonstrate the nested numbering fix for the guide editor.
This script tests both the text editor mode and rich editor mode.
"""

import requests
import time

BASE_URL = "http://127.0.0.1:5555"

def test_nested_numbering():
    """Test that nested numbering is preserved in text editor mode"""
    
    # Sample content with nested numbering
    test_content = """1 Main Step One
1.1 Sub-step under step one
1.1.1 Sub-sub-step under 1.1
1.1.2 Another sub-sub-step
1.2 Another sub-step under step one

2 Main Step Two
2.1 Sub-step under step two
2.1.1 Sub-sub-step under 2.1
2.1.2 Another sub-sub-step under 2.1
2.2 Another sub-step under step two

3 Main Step Three
3.1 Sub-step under step three"""

    print("Testing nested numbering preservation...")
    print("Original content:")
    print(test_content)
    print("\n" + "="*50 + "\n")
    
    # Create a session to maintain authentication
    session = requests.Session()
    
    # First, authenticate (you'll need to do this manually in the browser)
    print("Please authenticate in the browser first, then press Enter to continue...")
    input()
    
    # Test saving content with nested numbering
    content_data = {
        "tab_id": 1,  # Assuming tab 1 exists
        "content": test_content.replace('\n', '<br>')  # Convert to HTML format
    }
    
    try:
        response = session.post(f"{BASE_URL}/api/guide/content",
                               json=content_data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if "message" in result:
                print("✓ Content saved successfully!")
                
                # Verify the content was saved correctly
                time.sleep(1)
                response = session.get(f"{BASE_URL}/guide?tab=1")
                
                if "1.1.1" in response.text and "2.1.2" in response.text:
                    print("✓ Nested numbering preserved correctly!")
                    return True
                else:
                    print("✗ Nested numbering was not preserved")
                    return False
            else:
                print(f"✗ Save failed: {result}")
                return False
        else:
            print(f"✗ Request failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error during test: {e}")
        return False

if __name__ == "__main__":
    print("Nested Numbering Test for Guide Editor")
    print("=====================================")
    print()
    print("This test demonstrates the fix for preserving nested numbering")
    print("like 1.1.2 in the guide editor.")
    print()
    
    success = test_nested_numbering()
    
    if success:
        print("\n🎉 Test passed! Nested numbering is working correctly.")
        print("\nInstructions for using the fixed editor:")
        print("1. Click 'Edit' to start editing (defaults to Text Mode)")
        print("2. In Text Mode, type your nested numbering like:")
        print("   1 Step one")
        print("   1.1 Sub-step")
        print("   1.1.2 Sub-sub-step")
        print("3. Click 'Save' to preserve the exact formatting")
        print("4. Use 'Rich Mode' button for advanced formatting when needed")
    else:
        print("\n❌ Test failed. Please check the implementation.")
