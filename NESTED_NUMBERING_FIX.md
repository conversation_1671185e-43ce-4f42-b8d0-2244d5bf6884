# Nested Numbering Fix for Guide Editor

## Problem
When editing text in the guide with nested bullet numbering like:
```
1 step 1
1.1 step x 
1.1.2 step Y
```

The numbering was being flattened to:
```
1 step 1
1 step x
1 step Y
```

## Root Cause
The issue was caused by Quill.js rich text editor automatically converting numbered text into its own list format, which only supports simple sequential numbering (1, 2, 3...) rather than nested numbering (1.1, 1.1.2, etc.).

## Solution
Implemented a dual-editor approach with two modes:

### 1. Text Mode (Default)
- Uses a simple textarea that preserves exact formatting
- Perfect for nested numbering like 1.1.2
- Converts plain text to HTML while preserving line breaks and spacing
- Default mode when clicking "Edit"

### 2. Rich Mode
- Uses Quill.js for advanced formatting (bold, italic, colors, etc.)
- Available by clicking "Rich Mode" button during editing
- Best for content that needs rich formatting features

## Changes Made

### Template Changes (`templates/guide.html`)
1. **Added dual editor interface:**
   - Text editor container with textarea
   - Rich editor container with Quill.js
   - Mode switching buttons

2. **Updated JavaScript functions:**
   - `toggleTextEdit()` - Activates text mode
   - `toggleRichEdit()` - Activates rich mode
   - `saveTextContent()` - Saves plain text with HTML conversion
   - `cancelTextEdit()` - Cancels text editing
   - Updated existing rich editor functions

3. **Enhanced CSS:**
   - Styling for text editor container
   - Better formatting preservation

### Key Features
- **Preserves exact numbering:** 1.1.2 stays as 1.1.2
- **Mode switching:** Can switch between text and rich modes during editing
- **HTML conversion:** Plain text is properly converted to HTML with line breaks
- **Backward compatible:** Existing rich content continues to work

## Usage Instructions

### For Nested Numbering:
1. Click "Edit" (defaults to Text Mode)
2. Type your content with nested numbering:
   ```
   1 Main step
   1.1 Sub-step
   1.1.2 Sub-sub-step
   ```
3. Click "Save" to preserve exact formatting

### For Rich Formatting:
1. Click "Edit" then "Rich Mode"
2. Use Quill.js toolbar for formatting
3. Click "Save" to apply rich formatting

### Switching Modes:
- During editing, click "Rich Mode" to switch to rich editor
- Click "Text Mode" to switch back to plain text editor
- Content is preserved when switching modes

## Technical Details

### Text to HTML Conversion
The text editor converts plain text to HTML:
- Line breaks → `<br>` tags
- Multiple spaces → `&nbsp;` entities
- HTML entities are properly escaped

### Content Storage
- Content is stored as HTML in the CSV file
- Both modes save to the same backend endpoint
- Display uses the same HTML rendering

## Testing
Run `test_nested_numbering.py` to verify the fix works correctly.

## Benefits
1. **Preserves exact formatting** for numbered lists
2. **Maintains rich editing capabilities** when needed
3. **User-friendly interface** with clear mode indicators
4. **Backward compatible** with existing content
5. **Flexible workflow** - choose the right tool for the content type
