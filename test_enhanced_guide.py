#!/usr/bin/env python3
"""
Test script for enhanced 11.2 Guide functionality
Tests tab title editing and rich text content
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5555"

def login():
    """Login and return session"""
    session = requests.Session()
    login_data = {"password": "uproar321"}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200 and "raiders" in response.url:
        return session
    return None

def test_tab_title_editing(session):
    """Test tab title editing functionality"""
    print("Testing tab title editing...")
    
    # First, create a test tab
    tab_data = {
        "title": "Original Title",
        "order": 99
    }
    response = session.post(f"{BASE_URL}/api/guide/tabs", 
                           json=tab_data,
                           headers={"Content-Type": "application/json"})
    
    if response.status_code != 200:
        print(f"✗ Failed to create test tab: {response.status_code}")
        return False
    
    # Get the guide page to find the new tab ID
    response = session.get(f"{BASE_URL}/guide")
    if response.status_code != 200:
        print(f"✗ Failed to load guide page: {response.status_code}")
        return False
    
    # Find the tab with "Original Title" in the response
    if "Original Title" not in response.text:
        print("✗ Test tab not found in guide page")
        return False
    
    # Test updating the tab title (simulate what the frontend would do)
    # We'll assume the new tab got ID 4 (after the 3 default tabs)
    update_data = {
        "id": 4,
        "title": "Updated Title",
        "order": 99
    }
    response = session.post(f"{BASE_URL}/api/guide/tabs",
                           json=update_data,
                           headers={"Content-Type": "application/json"})
    
    if response.status_code == 200:
        result = response.json()
        if "message" in result:
            print("✓ Tab title update successful")
            
            # Verify the title was updated
            response = session.get(f"{BASE_URL}/guide")
            if "Updated Title" in response.text:
                print("✓ Tab title change reflected in page")
                return True
            else:
                print("✗ Tab title change not reflected in page")
                return False
        else:
            print(f"✗ Tab title update failed: {result}")
            return False
    else:
        print(f"✗ Tab title update request failed: {response.status_code}")
        return False

def test_rich_content_saving(session):
    """Test saving rich HTML content"""
    print("Testing rich content saving...")
    
    # Test saving HTML content with formatting
    rich_content = """
    <h2>Rich Content Test</h2>
    <p><strong>Bold text</strong> and <em>italic text</em></p>
    <ul>
        <li>Bullet point 1</li>
        <li>Bullet point 2</li>
    </ul>
    <p><a href="https://example.com">Test link</a></p>
    <blockquote>This is a quote</blockquote>
    """
    
    content_data = {
        "tab_id": 1,
        "content": rich_content
    }
    response = session.post(f"{BASE_URL}/api/guide/content",
                           json=content_data,
                           headers={"Content-Type": "application/json"})
    
    if response.status_code == 200:
        result = response.json()
        if "message" in result:
            print("✓ Rich content save successful")
            
            # Verify the content was saved and is displayed
            response = session.get(f"{BASE_URL}/guide?tab=1")
            if "<h2>Rich Content Test</h2>" in response.text:
                print("✓ Rich HTML content preserved and displayed")
                return True
            else:
                print("✗ Rich HTML content not properly displayed")
                return False
        else:
            print(f"✗ Rich content save failed: {result}")
            return False
    else:
        print(f"✗ Rich content save request failed: {response.status_code}")
        return False

def test_guide_page_loads_with_enhancements(session):
    """Test that the guide page loads with all enhancements"""
    print("Testing enhanced guide page loading...")
    
    response = session.get(f"{BASE_URL}/guide")
    if response.status_code != 200:
        print(f"✗ Guide page failed to load: {response.status_code}")
        return False
    
    # Check for Quill.js editor presence
    if "quill" in response.text.lower():
        print("✓ Quill.js rich text editor included")
    else:
        print("✗ Quill.js rich text editor not found")
        return False
    
    # Check for tab editing functionality
    if "editTabTitle" in response.text:
        print("✓ Tab title editing functionality present")
    else:
        print("✗ Tab title editing functionality not found")
        return False
    
    # Check for edit buttons in authenticated view
    if "tab-edit-btn" in response.text:
        print("✓ Tab edit buttons present for authenticated users")
    else:
        print("✗ Tab edit buttons not found")
        return False
    
    return True

def cleanup_test_data(session):
    """Clean up test data"""
    print("Cleaning up test data...")
    
    # Try to delete the test tab (ID 4)
    response = session.delete(f"{BASE_URL}/api/guide/tabs/4")
    if response.status_code == 200:
        print("✓ Test tab cleaned up")
    else:
        print("⚠ Could not clean up test tab (may not exist)")

def main():
    """Run all enhanced functionality tests"""
    print("Starting enhanced 11.2 Guide functionality tests...\n")
    
    # Login
    session = login()
    if not session:
        print("✗ Login failed. Cannot run authenticated tests.")
        return
    
    print("✓ Login successful")
    
    # Test enhanced page loading
    if not test_guide_page_loads_with_enhancements(session):
        print("Enhanced page loading test failed.")
        return
    
    # Test tab title editing
    if not test_tab_title_editing(session):
        print("Tab title editing test failed.")
        return
    
    # Test rich content
    if not test_rich_content_saving(session):
        print("Rich content test failed.")
        return
    
    # Cleanup
    cleanup_test_data(session)
    
    print("\n✓ All enhanced functionality tests passed!")
    print("The guide now supports:")
    print("  • Tab title editing")
    print("  • Rich text editing with formatting")
    print("  • HTML content preservation")
    print("  • Enhanced user interface")

if __name__ == "__main__":
    main()
